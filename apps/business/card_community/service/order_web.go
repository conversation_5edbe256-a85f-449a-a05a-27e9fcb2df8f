package service

import (
	"errors"
	"strconv"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model/mongdb"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/wat"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// CreateOrder 创建交易订单
// 在聊天中创建交易订单，包含卡片信息和价格
func (s *Service) CreateOrder(req *define.CreateOrderRequest) (*define.CreateOrderResponse, error) {
	userID := s.userService.GetUserId()

	// 验证卡片组数 = 卡片数组长度
	if req.CardGroupsCount != len(req.CardItems) {
		return nil, commondefine.ParamErr.SetMsg("卡片组数与卡片数组长度不一致")
	}

	// 验证总数量 = 所有卡片Quantity之和
	totalQuantity := 0
	for _, item := range req.CardItems {
		if item.Quantity <= 0 {
			return nil, commondefine.ParamErr.SetMsg("卡片数量必须大于0")
		}
		totalQuantity += item.Quantity
	}
	if req.TotalQuantity != totalQuantity {
		return nil, commondefine.ParamErr.SetMsg("总数量与卡片数量之和不一致")
	}

	// 当前登录用户作为卖家，买家从请求中获取
	sellerID := userID
	buyerID := req.BuyerID

	if buyerID == sellerID {
		return nil, commondefine.ParamErr.SetMsg("不能向自己创建订单")
	}

	// 校验买家ID是否有效
	buyerUser, err := facade.GetNodeUser(s.ctx, buyerID)
	if err != nil || buyerUser == nil {
		log.Ctx(s.ctx).Errorf("获取买家信息失败: %v", err)
		return nil, commondefine.ParamErr.SetMsg("买家用户不存在")
	}

	// 校验卖家待支付订单数量限制（不能超过5单）
	if err := logic.CheckSellerUnpaidOrderLimit(s.ctx, sellerID); err != nil {
		return nil, err
	}

	// 查找买家和卖家之间的现有会话
	conversation := logic.FindExistingConversation(s.ctx, sellerID, buyerID)
	if conversation == nil {
		log.Ctx(s.ctx).Infof("用户 %s 和 %s 之间没有会话记录，不能创建订单", sellerID, buyerID)
		return nil, define.CC500407Err.SetMsg("会话不存在，暂时无法创建订单")
	}

	// 验证会话状态是否正常
	if conversation.Status == -1 {
		log.Ctx(s.ctx).Infof("用户 %s 和 %s 之间的会话状态为限制中，不能创建订单", sellerID, buyerID)
		return nil, define.CC500406Err.SetMsg("在 ta 回复前，你还不能给 ta 发送交易订单哦～")
	}

	// 生成订单ID
	orderID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)

	// 计算订单过期时间（24小时后）
	expiredAt := time.Now().Add(24 * time.Hour)

	// 获取第一张卡牌图片URL
	firstCardImageURL := req.CardItems[0].FrontImageURL

	// 创建订单模型
	order := &model.CardOrder{
		ID:                  orderID,
		ConversationGroupID: conversation.ConversationGroupID, // 使用找到的会话组ID关联
		BuyerID:             buyerID,
		SellerID:            sellerID,
		FirstCardImageURL:   firstCardImageURL,
		CardGroupsCount:     int32(req.CardGroupsCount),
		TotalQuantity:       int32(req.TotalQuantity),
		TotalAmount:         req.TotalAmount,
		PayAmount:           0, // 创建订单时实付金额为0，表示待支付状态，支付成功后设置为实际支付金额
		Status:              enums.OrderStatusUnPaid.Int32(),
		ExpiredAt:           expiredAt,
	}

	// 设置卡片信息
	if err := order.SetCardItems(req.CardItems); err != nil {
		log.Ctx(s.ctx).Errorf("设置卡片信息失败: %v", err)
		return nil, commondefine.ParamErr.SetMsg("卡片信息格式错误")
	}

	// 保存订单到数据库
	query := repo.GetQuery()
	cardOrderRepo := repo.NewCardOrderRepo(query.CardOrder.WithContext(s.ctx))
	if err := cardOrderRepo.Save(order); err != nil {
		log.Ctx(s.ctx).Errorf("保存订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("创建订单失败")
	}

	// 异步发送推送通知给买家
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 发送订单消息到聊天
		orderSnapshot := define.OrderSnapshot{
			OrderID:           order.ID,
			FirstCardImageURL: order.FirstCardImageURL,
			CardGroupsCount:   int(order.CardGroupsCount),
			TotalQuantity:     int(order.TotalQuantity),
			TotalAmount:       order.TotalAmount,
			Status:            order.GetStatus(),
		}
		if err := logic.SendOrderMessageOnOrderChange(spanCtx, conversation.ConversationGroupID, sellerID, buyerID, orderID, &orderSnapshot, conversation.LastMessageID); err != nil {
			log.Ctx(spanCtx).Errorf("发送订单消息失败: %v", err)
			// 不返回错误，因为订单已经创建成功，消息发送失败不影响主流程
		}

		// 消息推送
		var sellerNickname string
		if sellerUser, err := facade.GetNodeUser(spanCtx, sellerID); err == nil && sellerUser != nil {
			sellerNickname = sellerUser.PatbgDetail.Nickname
		}

		// 获取订单对象用于推送
		orderObj := &model.CardOrder{
			ID:     orderID,
			Status: int32(enums.OrderStatusUnPaid),
		}
		if err := logic.PushOrderNotification(spanCtx, orderObj, buyerID, sellerNickname); err != nil {
			log.Ctx(spanCtx).Errorf("推送订单通知失败: %v", err)
		}
	}()

	log.Ctx(s.ctx).Infof("卖家 %s 向买家 %s 创建订单成功，订单ID: %s", sellerID, buyerID, orderID)

	return &define.CreateOrderResponse{
		OrderID: orderID,
	}, nil
}

// GetOrderDetail 获取订单详情
// 获取指定订单的详细信息，包含买家卖家信息和订单状态
func (s *Service) GetOrderDetail(req *define.GetOrderDetailRequest) (*define.OrderDetailResponse, error) {
	userID := s.userService.GetUserId()

	// 从数据库查询订单详情
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证用户是否有权限查看该订单（买家或卖家）
	if !order.IsBuyer(userID) && !order.IsSeller(userID) {
		return nil, define.CC500404Err.SetMsg("无权限查看该订单")
	}

	// 验证订单是否被当前用户删除
	if order.IsDeletedByUser(userID) {
		return nil, define.CC500402Err.SetMsg("订单不存在")
	}

	// 获取卡片信息
	cardItems, err := order.GetCardItems()
	if err != nil {
		log.Ctx(s.ctx).Errorf("解析卡片信息失败: %v", err)
		cardItems = []define.CardItem{}
	}

	// 获取收货地址
	shippingAddress, err := order.GetShippingAddress()
	if err != nil {
		log.Ctx(s.ctx).Errorf("解析收货地址失败: %v", err)
		shippingAddress = nil
	}

	// 批量获取用户信息
	userIDs := []string{order.BuyerID, order.SellerID}
	nodeUserMap, err := facade.GetNodeUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		nodeUserMap = make(map[string]*mongdb.User)
	}

	// 构造用户信息
	var buyerInfo, sellerInfo define.UserInfo

	buyerUser := nodeUserMap[order.BuyerID]
	buyerInfo = define.UserInfo{
		ID:     order.BuyerID,
		Name:   buyerUser.PatbgDetail.Nickname,
		Avatar: buyerUser.PatbgDetail.Avatar,
	}

	sellerUser := nodeUserMap[order.SellerID]
	sellerInfo = define.UserInfo{
		ID:     order.SellerID,
		Name:   sellerUser.PatbgDetail.Nickname,
		Avatar: sellerUser.PatbgDetail.Avatar,
	}

	// 构造响应
	response := &define.OrderDetailResponse{
		OrderID:         order.ID,
		BuyerID:         order.BuyerID,
		BuyerInfo:       buyerInfo,
		SellerID:        order.SellerID,
		SellerInfo:      sellerInfo,
		CardItems:       cardItems,
		CardGroupsCount: int(order.CardGroupsCount),
		TotalQuantity:   int(order.TotalQuantity),
		TotalAmount:     order.TotalAmount,
		PayAmount:       order.PayAmount,
		PaymentMethod:   order.GetPaymentMethod(),
		Status:          order.GetStatus(),
		ShippingAddress: shippingAddress,
		PaymentAt:       order.PaymentAt,
		DeliveredAt:     order.DeliveredAt,
		ReceivedAt:      order.ReceivedAt,
		ExpiredAt:       order.ExpiredAt,
		CreatedAt:       order.CreatedAt,
		CancelType:      order.GetCancelType(),
	}

	log.Ctx(s.ctx).Infof("用户 %s 查询订单详情: %s", userID, req.OrderID)

	return response, nil
}

// GetOrderList 获取订单列表（统一接口）
// 根据用户类型获取买家或卖家的订单列表，支持状态筛选和分页
func (s *Service) GetOrderList(req *define.GetOrderListRequest) (*define.GetOrderListResponse, error) {
	userID := s.userService.GetUserId()

	// 构建查询条件
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder

	var queryBuilder *search.QueryBuilder
	if req.UserType == enums.UserTypeBuyer { // 1=买家
		// 买家视角：查询买家订单
		queryBuilder = search.NewQueryBuilder().
			Eq(cardOrderSchema.BuyerID, userID).
			Eq(cardOrderSchema.BuyerDeleted, false). // 未被买家删除
			OrderByDesc(cardOrderSchema.CreatedAt)   // 按创建时间倒序
	} else { // 2=卖家
		// 卖家视角：查询卖家订单
		queryBuilder = search.NewQueryBuilder().
			Eq(cardOrderSchema.SellerID, userID).
			Eq(cardOrderSchema.SellerDeleted, false). // 未被卖家删除
			OrderByDesc(cardOrderSchema.CreatedAt)    // 按创建时间倒序
	}

	// 状态筛选
	if req.Status != nil {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
	}

	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	// 查询订单列表
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))
	orders, err := cardOrderRepo.SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单列表失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单列表失败")
	}

	// 批量获取对方用户信息
	otherUserIDs := make([]string, 0, len(orders))
	for _, order := range orders {
		if req.UserType == enums.UserTypeBuyer { // 买家视角：获取卖家信息
			otherUserIDs = append(otherUserIDs, order.SellerID)
		} else { // 卖家视角：获取买家信息
			otherUserIDs = append(otherUserIDs, order.BuyerID)
		}
	}

	var otherUserMap map[string]*mongdb.User
	if len(otherUserIDs) > 0 {
		var err error
		otherUserMap, err = facade.GetNodeUserMap(s.ctx, otherUserIDs)
		if err != nil {
			log.Ctx(s.ctx).Errorf("批量获取信息失败: %v", err)
			otherUserMap = make(map[string]*mongdb.User)
		}
	}

	// 转换为响应格式
	list := make([]*define.OrderListItem, 0, len(orders))
	for _, order := range orders {
		var otherPartyID string
		var otherPartyInfo define.UserInfo

		if req.UserType == enums.UserTypeBuyer { // 买家视角：对方是卖家
			otherPartyID = order.SellerID
			sellerUser := otherUserMap[order.SellerID]
			otherPartyInfo = define.UserInfo{
				ID:     order.SellerID,
				Name:   sellerUser.PatbgDetail.Nickname,
				Avatar: sellerUser.PatbgDetail.Avatar,
			}
		} else { // 卖家视角：对方是买家
			otherPartyID = order.BuyerID
			buyerUser := otherUserMap[order.BuyerID]
			otherPartyInfo = define.UserInfo{
				ID:     order.BuyerID,
				Name:   buyerUser.PatbgDetail.Nickname,
				Avatar: buyerUser.PatbgDetail.Avatar,
			}
		}

		item := &define.OrderListItem{
			OrderID:           order.ID,
			OtherPartyID:      otherPartyID,
			OtherPartyInfo:    otherPartyInfo,
			FirstCardImageURL: order.FirstCardImageURL,
			CardGroupsCount:   int(order.CardGroupsCount),
			TotalQuantity:     int(order.TotalQuantity),
			TotalAmount:       order.TotalAmount,
			Status:            order.GetStatus(),
			CreatedAt:         order.CreatedAt,
		}
		list = append(list, item)
	}

	// 计算是否有更多数据（用户端分页使用HasMore格式）
	hasMore := len(list) == req.GetPageSize()

	userTypeStr := req.UserType.String()
	log.Ctx(s.ctx).Infof("用户 %s 查询%s订单列表，返回 %d 条记录", userID, userTypeStr, len(list))

	return &define.GetOrderListResponse{
		List:    list,
		HasMore: hasMore,
	}, nil
}

// PayOrder 支付订单
// 处理订单支付流程，包括实名认证和收货地址校验
func (s *Service) PayOrder(req *define.PayOrderRequest) (*define.PayOrderResponse, error) {
	userID := s.userService.GetUserId()

	// 查询订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证订单是否属于当前用户（买家）
	if !order.IsBuyer(userID) {
		return nil, define.CC500404Err.SetMsg("无权限操作该订单")
	}

	// 验证订单状态是否可支付
	if !order.CanPay() {
		if order.IsExpired() {
			return nil, define.CC500405Err.SetMsg("订单已过期")
		}
		return nil, define.CC500407Err.SetMsg("订单状态不允许支付")
	}

	// 验证用户实名认证状态
	if err := logic.CheckUserRealName(s.ctx, userID); err != nil {
		return nil, err
	}

	// 校验支付密码
	checkPayPwdReq := &wat.CheckPayPwdReq{
		UserId: userID,
		Pwd:    req.PayPwd,
	}
	if err := wat.CheckPayPwd(s.ctx, checkPayPwdReq); err != nil {
		log.Ctx(s.ctx).Errorf("支付密码校验失败: %v", err)
		return nil, define.CC500705Err.SetMsg("支付密码错误")
	}

	// 调用浦发卡牌钱包支付接口
	payReq := &wat.SpdbCardBalancePayReq{
		BuyUserId:  userID,
		SaleUserId: order.SellerID,
		Amount:     order.TotalAmount,
		OrderId:    req.OrderID,
	}

	// 执行支付
	payResp, err := wat.SpdbCardBalancePay(s.ctx, payReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("调用浦发支付接口失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("支付失败，请稍后重试")
	}

	// 从支付响应中获取流水号
	transactionNo := payResp.TrId

	// 二次查询确认支付状态
	statusReq := &wat.SpdbCardPayStatusReq{
		BuyUserId: userID,
		OrderId:   req.OrderID,
	}

	paymentStatus, err := wat.SpdbCardPayStatus(s.ctx, statusReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询支付状态失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("支付状态确认失败")
	}

	// 判断支付状态
	if !paymentStatus {
		log.Ctx(s.ctx).Errorf("支付状态确认失败，订单: %s, 状态: %v", req.OrderID, paymentStatus)
		return nil, commondefine.CommonErr.SetMsg("支付未成功，请重试")
	}

	log.Ctx(s.ctx).Infof("支付成功，订单: %s, 流水号: %s, 状态确认: %v", req.OrderID, transactionNo, paymentStatus)

	paymentMethod := enums.PaymentMethodCardWallet // 默认支付方式：卡牌钱包

	// 设置支付信息
	order.SetPaymentInfo(paymentMethod, transactionNo, order.TotalAmount)
	order.SetStatus(enums.OrderStatusUnDelivered)

	// 设置收货地址
	if err := order.SetShippingAddress(&req.ShippingAddress); err != nil {
		log.Ctx(s.ctx).Errorf("设置收货地址失败: %v", err)
		return nil, commondefine.ParamErr.SetMsg("收货地址格式错误")
	}

	// 更新订单
	if err := cardOrderRepo.UpdateById(order); err != nil {
		log.Ctx(s.ctx).Errorf("更新订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("支付失败")
	}

	// 异步发送推送通知给卖家
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 发送订单支付成功消息到聊天
		orderSnapshot := define.OrderSnapshot{
			OrderID:           order.ID,
			FirstCardImageURL: order.FirstCardImageURL,
			CardGroupsCount:   int(order.CardGroupsCount),
			TotalQuantity:     int(order.TotalQuantity),
			TotalAmount:       order.TotalAmount,
			Status:            order.GetStatus(),
		}
		// 获取会话信息以获取LastMessageID
		conversation := logic.FindExistingConversation(spanCtx, order.SellerID, order.BuyerID)
		lastMessageID := ""
		if conversation != nil {
			lastMessageID = conversation.LastMessageID
		}

		if err := logic.SendOrderMessageOnOrderChange(spanCtx, order.ConversationGroupID, order.BuyerID, order.SellerID, req.OrderID, &orderSnapshot, lastMessageID); err != nil {
			log.Ctx(spanCtx).Errorf("发送订单支付消息失败: %v", err)
			// 不返回错误，因为支付已经成功，消息发送失败不影响主流程
		}

		// 消息推送
		var buyerNickname string
		if buyerUser, err := facade.GetNodeUser(spanCtx, order.BuyerID); err == nil && buyerUser != nil {
			buyerNickname = buyerUser.PatbgDetail.Nickname
		}

		// 更新订单状态用于推送
		order.Status = int32(enums.OrderStatusUnDelivered)
		if err := logic.PushOrderNotification(spanCtx, order, order.SellerID, buyerNickname); err != nil {
			log.Ctx(spanCtx).Errorf("推送支付通知失败: %v", err)
		}
	}()

	log.Ctx(s.ctx).Infof("用户 %s 支付订单成功: %s, 交易号: %s", userID, req.OrderID, transactionNo)

	return &define.PayOrderResponse{
		OrderID: req.OrderID,
	}, nil
}

// CancelOrder 取消订单
// 根据取消类型取消订单，更新订单状态
func (s *Service) CancelOrder(req *define.CancelOrderRequest) (*define.OrderOperationResponse, error) {
	userID := s.userService.GetUserId()

	// 查询订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证用户是否有权限取消该订单
	if !order.IsBuyer(userID) && !order.IsSeller(userID) {
		return nil, define.CC500404Err.SetMsg("无权限操作该订单")
	}

	// 验证订单状态是否可取消
	if !order.CanCancel() {
		return nil, define.CC500407Err.SetMsg("订单状态不允许取消")
	}

	// 确定取消类型
	var cancelType enums.CancelType
	if order.IsBuyer(userID) {
		cancelType = enums.CancelTypeBuyer
	} else {
		cancelType = enums.CancelTypeSeller
	}

	// 更新订单状态
	order.SetStatus(enums.OrderStatusCanceled)
	order.SetCancelInfo(cancelType)

	if err := cardOrderRepo.UpdateById(order); err != nil {
		log.Ctx(s.ctx).Errorf("更新订单状态失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("取消订单失败")
	}

	log.Ctx(s.ctx).Infof("用户 %s 取消订单成功: %s, 取消类型: %s", userID, req.OrderID, cancelType.String())

	return &define.OrderOperationResponse{
		OrderID: req.OrderID,
	}, nil
}

// DeleteOrder 删除订单
// 软删除订单记录，仅对当前用户隐藏
func (s *Service) DeleteOrder(req *define.DeleteOrderRequest) (*define.OrderOperationResponse, error) {
	userID := s.userService.GetUserId()

	// 查询订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证用户是否有权限删除该订单
	if !order.IsBuyer(userID) && !order.IsSeller(userID) {
		return nil, define.CC500404Err.SetMsg("无权限操作该订单")
	}

	// 验证订单是否已被当前用户删除
	if order.IsDeletedByUser(userID) {
		return nil, define.CC500402Err.SetMsg("订单不存在")
	}

	// 验证订单状态是否可删除（只有已取消或已完成的订单可删除）
	if !order.CanDelete() {
		return nil, define.CC500407Err.SetMsg("订单状态不允许删除")
	}

	// 软删除订单（标记为已删除，不真正删除数据）
	order.MarkDeletedByUser(userID)

	if err := cardOrderRepo.UpdateById(order); err != nil {
		log.Ctx(s.ctx).Errorf("删除订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("删除订单失败")
	}

	log.Ctx(s.ctx).Infof("用户 %s 删除订单成功: %s", userID, req.OrderID)

	return &define.OrderOperationResponse{
		OrderID: req.OrderID,
	}, nil
}

// ShipOrder 卖家发货
// 卖家确认发货，更新订单状态为待收货
func (s *Service) ShipOrder(req *define.ShipOrderRequest) (*define.OrderOperationResponse, error) {
	userID := s.userService.GetUserId()

	// 查询订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证当前用户是否为该订单的卖家
	if !order.IsSeller(userID) {
		return nil, define.CC500404Err.SetMsg("无权限操作该订单")
	}

	// 验证订单状态是否可发货
	if !order.CanShip() {
		return nil, define.CC500407Err.SetMsg("订单状态不允许发货")
	}

	// 更新订单状态为待收货
	order.SetStatus(enums.OrderStatusUnReceive)
	order.SetDeliveryInfo()

	if err := cardOrderRepo.UpdateById(order); err != nil {
		log.Ctx(s.ctx).Errorf("更新订单状态失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("发货失败")
	}

	// 异步发送推送通知给买家
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 获取卖家昵称
		var sellerNickname string
		if sellerUser, err := facade.GetNodeUser(spanCtx, order.SellerID); err == nil && sellerUser != nil {
			sellerNickname = sellerUser.PatbgDetail.Nickname
		}

		pushReq := &define.PushOrderRequest{
			UserID:      order.BuyerID,
			OrderID:     req.OrderID,
			PushType:    enums.PushTypeOrderUnReceive,
			FromUser:    sellerNickname,
			OrderStatus: enums.OrderStatusUnReceive,
		}
		relateInfo := notidefine.PushRelateInfo{
			RelateType:  notienums.PushRelateTypeCardCommunityOrder,
			RelateScene: notienums.PushRelateSceneUnReceived,
			RelateID:    util.StrVal(req.OrderID),
		}
		if err := logic.PushOrderNotification(spanCtx, pushReq, relateInfo); err != nil {
			log.Ctx(spanCtx).Errorf("推送发货通知失败: %v", err)
		}
	}()

	log.Ctx(s.ctx).Infof("卖家 %s 发货订单成功: %s", userID, req.OrderID)

	return &define.OrderOperationResponse{
		OrderID: req.OrderID,
	}, nil
}

// ConfirmReceived 买家确认收货
// 买家确认收货，完成订单交易
func (s *Service) ConfirmReceived(req *define.ConfirmOrderRequest) (*define.OrderOperationResponse, error) {
	userID := s.userService.GetUserId()

	// 查询订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.CC500402Err.SetMsg("订单不存在")
		}
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 验证当前用户是否为该订单的买家
	if !order.IsBuyer(userID) {
		return nil, define.CC500404Err.SetMsg("无权限操作该订单")
	}

	// 验证订单状态是否可确认收货
	if !order.CanConfirmReceived() {
		return nil, define.CC500407Err.SetMsg("订单状态不允许确认收货")
	}

	// 更新订单状态为已完成
	order.SetStatus(enums.OrderStatusCompleted)
	order.SetReceiveInfo() // 设置收货时间和完成时间

	if err := cardOrderRepo.UpdateById(order); err != nil {
		log.Ctx(s.ctx).Errorf("更新订单状态失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("确认收货失败")
	}

	// 处理资金结算给卖家 - 调用余额支付完成接口
	payDoneReq := &wat.SpdbCardBalancePayDoneReq{
		BuyUserId:  order.BuyerID,
		SaleUserId: order.SellerID,
		Amount:     order.PayAmount, // 使用实际支付金额
		OrderId:    req.OrderID,
	}

	// 执行资金结算
	payDoneResp, err := wat.SpdbCardBalancePayDone(s.ctx, payDoneReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("调用资金结算接口失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("资金结算失败")
	}

	// 从结算响应中获取流水号
	transactionNo := payDoneResp.TrId

	// 二次查询确认结算状态
	doneStatusReq := &wat.SpdbCardPayDoneStatusReq{
		SaleUserId: order.SellerID,
		OrderId:    req.OrderID,
	}

	settlementStatus, err := wat.SpdbCardPayDoneStatus(s.ctx, doneStatusReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询结算状态失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("结算状态确认失败")
	}

	// 判断结算状态
	if !settlementStatus {
		log.Ctx(s.ctx).Errorf("资金结算状态确认失败，订单: %s, 状态: %v", req.OrderID, settlementStatus)
		return nil, commondefine.CommonErr.SetMsg("资金结算未成功")
	}

	log.Ctx(s.ctx).Infof("资金结算成功，订单: %s, 结算流水号: %s, 状态确认: %v", req.OrderID, transactionNo, settlementStatus)

	// 异步发送推送通知给卖家
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 获取买家昵称
		var buyerNickname string
		if buyerUser, err := facade.GetNodeUser(spanCtx, order.BuyerID); err == nil && buyerUser != nil {
			buyerNickname = buyerUser.PatbgDetail.Nickname
		}

		pushReq := &define.PushOrderRequest{
			UserID:      order.SellerID,
			OrderID:     req.OrderID,
			PushType:    enums.PushTypeOrderCompleted,
			FromUser:    buyerNickname,
			OrderStatus: enums.OrderStatusCompleted,
		}
		relateInfo := notidefine.PushRelateInfo{
			RelateType:  notienums.PushRelateTypeCardCommunityOrder,
			RelateScene: notienums.PushRelateSceneCompleted,
			RelateID:    util.StrVal(req.OrderID),
		}
		if err := logic.PushOrderNotification(spanCtx, pushReq, relateInfo); err != nil {
			log.Ctx(spanCtx).Errorf("推送交易完成通知失败: %v", err)
		}
	}()

	log.Ctx(s.ctx).Infof("买家 %s 确认收货订单成功: %s", userID, req.OrderID)

	return &define.OrderOperationResponse{
		OrderID: req.OrderID,
	}, nil
}

// GetOrderStats 获取订单状态统计
// 一次查询所有相关订单，然后代码统计各状态数量
func (s *Service) GetOrderStats(req *define.GetOrderStatsRequest) (*define.OrderStatsResponse, error) {
	userID := s.userService.GetUserId()

	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder

	// 查询买家订单（只查询：待支付、待发货、待收货）
	buyerCardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))
	buyerQueryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.BuyerID, userID).
		Eq(cardOrderSchema.BuyerDeleted, false).
		In(cardOrderSchema.Status, []int32{
			enums.OrderStatusUnPaid.Int32(),
			enums.OrderStatusUnDelivered.Int32(),
			enums.OrderStatusUnReceive.Int32(),
		}).
		Build()

	buyerOrders, err := buyerCardOrderRepo.SelectList(buyerQueryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询买家订单失败: %v", err)
		buyerOrders = []*model.CardOrder{}
	}
	log.Ctx(s.ctx).Infof("用户 %s 查询到买家订单数量: %d", userID, len(buyerOrders))

	// 查询卖家订单（只查询：待发货）
	sellerCardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))
	sellerQueryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.SellerID, userID).
		Eq(cardOrderSchema.SellerDeleted, false).
		In(cardOrderSchema.Status, []int32{
			//enums.OrderStatusUnPaid.Int32(),
			enums.OrderStatusUnDelivered.Int32(),
			//enums.OrderStatusUnReceive.Int32(),
		}).
		Build()

	sellerOrders, err := sellerCardOrderRepo.SelectList(sellerQueryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询卖家订单失败: %v", err)
		sellerOrders = []*model.CardOrder{}
	}
	log.Ctx(s.ctx).Infof("用户 %s 查询到卖家订单数量: %d", userID, len(sellerOrders))

	// 统计买家订单各状态
	var buyerUnPaidCount, buyerUnDeliveredCount, buyerUnReceiveCount int64
	for _, order := range buyerOrders {
		log.Ctx(s.ctx).Infof("买家订单 %s 状态: %d (%s)", order.ID, order.Status, order.GetStatus().String())
		switch order.GetStatus() {
		case enums.OrderStatusUnPaid:
			buyerUnPaidCount++
		case enums.OrderStatusUnDelivered:
			buyerUnDeliveredCount++
		case enums.OrderStatusUnReceive:
			buyerUnReceiveCount++
		}
	}

	// 统计卖家订单各状态
	var sellerUnPaidCount, sellerUnDeliveredCount, sellerUnReceiveCount int64
	for _, order := range sellerOrders {
		log.Ctx(s.ctx).Infof("卖家订单 %s 状态: %d (%s)", order.ID, order.Status, order.GetStatus().String())
		switch order.GetStatus() {
		case enums.OrderStatusUnPaid:
			sellerUnPaidCount++
		case enums.OrderStatusUnDelivered:
			sellerUnDeliveredCount++
		case enums.OrderStatusUnReceive:
			sellerUnReceiveCount++
		}
	}

	log.Ctx(s.ctx).Infof("用户 %s 订单统计 - 买家(待支付:%d,待发货:%d,待收货:%d) 卖家(待支付:%d,待发货:%d,待收货:%d)",
		userID, buyerUnPaidCount, buyerUnDeliveredCount, buyerUnReceiveCount,
		sellerUnPaidCount, sellerUnDeliveredCount, sellerUnReceiveCount)

	return &define.OrderStatsResponse{
		BuyerStats: &define.OrderStatusStats{
			UnPaidCount:      buyerUnPaidCount,
			UnDeliveredCount: buyerUnDeliveredCount,
			UnReceiveCount:   buyerUnReceiveCount,
		},
		SellerStats: &define.OrderStatusStats{
			UnPaidCount:      sellerUnPaidCount,
			UnDeliveredCount: sellerUnDeliveredCount,
			UnReceiveCount:   sellerUnReceiveCount,
		},
	}, nil
}
