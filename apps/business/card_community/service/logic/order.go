package logic

import (
	"context"
	"fmt"
	"strings"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	"app_service/global"
	"app_service/third_party/pat"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

// CheckUserRealName 检查用户是否已实名认证
func CheckUserRealName(ctx context.Context, userID string) error {
	if userID == "" {
		return define.CC500701Err.SetMsg("用户ID不能为空")
	}

	realInfos, err := pat.GetRealInfo(ctx, []string{userID})
	if err != nil {
		log.Ctx(ctx).Errorf("获取用户实名信息失败: %v", err)
		return define.CC500701Err.SetMsg("获取用户实名信息失败")
	}

	if len(realInfos) == 0 || realInfos[0].RealName == "" {
		return define.CC500701Err.SetMsg("请先完成实名认证")
	}

	return nil
}

// MaskAddress 脱敏收货地址信息
func MaskAddress(address *define.Address) *define.Address {
	if address == nil {
		return nil
	}

	return &define.Address{
		Name:        MaskReceiverName(address.Name),
		MobilePhone: MaskPhoneNumber(address.MobilePhone),
		Code:        address.Code,
		Area:        MaskArea(address.Area),
		Place:       MaskPlace(address.Place),
	}
}

// MaskReceiverName 脱敏收货人姓名：只显示姓氏
func MaskReceiverName(name string) string {
	if len(name) == 0 {
		return ""
	}
	runes := []rune(name)
	if len(runes) == 1 {
		return string(runes[0]) + "*"
	}
	return string(runes[0]) + "*"
}

// MaskPhoneNumber 脱敏手机号：133****1234
func MaskPhoneNumber(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MaskArea 脱敏区域信息：只保留到"区"
func MaskArea(area string) string {
	if area == "" {
		return ""
	}
	// 查找"区"字的位置
	if idx := strings.Index(area, "区"); idx != -1 {
		return area[:idx+len("区")]
	}
	// 如果没有"区"字，返回前几个字符
	runes := []rune(area)
	if len(runes) > 3 {
		return string(runes[:3]) + "..."
	}
	return area
}

// MaskPlace 脱敏详细地址：全模糊处理
func MaskPlace(place string) string {
	if place == "" {
		return ""
	}
	return "***"
}

// PushOrderNotification 推送订单相关通知
// 根据订单状态变化向相关用户推送通知
func PushOrderNotification(ctx context.Context, order *model.CardOrder, userID string, fromUserNickname string) error {
	// 参数验证
	if order == nil {
		log.Ctx(ctx).Error("推送订单通知失败: 订单对象不能为空")
		return nil // 推送失败不影响主业务流程
	}
	if userID == "" {
		log.Ctx(ctx).Error("推送订单通知失败: 用户ID不能为空")
		return nil
	}
	if !order.GetStatus().IsValid() {
		log.Ctx(ctx).Error("推送订单通知失败: 订单状态无效")
		return nil
	}

	// 构造推送标题和内容
	title := order.GetStatus().GetPushTitle()
	fromUser := fromUserNickname
	if fromUser == "" {
		fromUser = "用户"
	}
	content := "来自 " + fromUser

	// 构造extras，包含跳转URL
	baseURL := "https://sit-wcjs.ahbq.com.cn"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		baseURL = "https://wcjs.ahbq.com.cn"
	}
	pageURL := fmt.Sprintf("ojb://webview_h5?url=%s/pages/Card/OrderPayment/index?id=%s", baseURL, order.ID)
	extras := map[string]interface{}{
		"url": pageURL,
	}

	// 构造relateInfo
	var relateScene notienums.PushRelateSceneEnum
	switch order.GetStatus() {
	case enums.OrderStatusUnPaid:
		relateScene = notienums.PushRelateSceneUnPaid
	case enums.OrderStatusUnDelivered:
		relateScene = notienums.PushRelateSceneUnDelivered
	case enums.OrderStatusUnReceive:
		relateScene = notienums.PushRelateSceneUnReceived
	case enums.OrderStatusCompleted:
		relateScene = notienums.PushRelateSceneCompleted
	default:
		relateScene = notienums.PushRelateSceneUnPaid // 默认场景
	}

	relateInfo := notidefine.PushRelateInfo{
		RelateType:  notienums.PushRelateTypeCardCommunityOrder,
		RelateScene: relateScene,
		RelateID:    order.ID,
	}

	// 构造推送消息参数
	message := notidefine.PushMessage{
		Title:         title,
		Content:       content,
		AudienceType:  notienums.PushAudienceTypeUser,
		UserIDs:       []string{userID},
		AndroidExtras: extras,
		IOSExtras:     extras,
	}

	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		log.Ctx(ctx).Errorf("订单推送通知发送失败 - 用户: %s, 错误: %v", userID, err)
		return nil // 推送失败不影响主业务流程
	}

	log.Ctx(ctx).Infof("订单推送通知发送成功 - 用户: %s, 订单: %s, 状态: %s, 推送总数: %d, 成功: %d, 失败: %d",
		userID, order.ID, order.GetStatus().String(), result.SendTotal, result.SuccessCount, result.FailCount)

	return nil
}
